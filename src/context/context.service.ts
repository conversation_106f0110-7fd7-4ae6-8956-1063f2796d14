import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { readFileSync } from 'fs';
import { SYSTEM_PROMPT_FILE_PATH, DATABASE_METADATA_FILE_PATH } from './constants';

@Injectable()
export class ContextService implements OnModuleInit {
  private readonly logger = new Logger(ContextService.name);
  private systemPrompt: string;
  private databaseMetadata: string;

  async onModuleInit() {
    this.logger.log('Initializing Context Service - loading required text files');
    this.loadSystemPrompt();
    this.loadDatabaseMetadata();
    this.logger.log('Context Service initialized successfully');
  }

  getSystemPromptWithContext(): string {
    this.logger.log('Generating system prompt with database context');

    let systemPrompt = this.systemPrompt;
    systemPrompt += '\n\n## Database Metadata\n\n' + this.databaseMetadata;

    return systemPrompt + ' /no_think';
  }

  private loadSystemPrompt(): void {
    try {
      this.systemPrompt = readFileSync(SYSTEM_PROMPT_FILE_PATH, 'utf-8');
      this.logger.log(`Loaded system prompt from: ${SYSTEM_PROMPT_FILE_PATH}`);
    } catch (error) {
      this.logger.error(`CRITICAL: Failed to load required system prompt from ${SYSTEM_PROMPT_FILE_PATH}: ${error.message}`);
      throw new Error(`Context Service initialization failed: Cannot load system prompt from ${SYSTEM_PROMPT_FILE_PATH}. This file is required for the application to function.`);
    }
  }

  private loadDatabaseMetadata(): void {
    try {
      this.databaseMetadata = readFileSync(DATABASE_METADATA_FILE_PATH, 'utf-8');
      this.logger.log(`Loaded database metadata from: ${DATABASE_METADATA_FILE_PATH}`);
    } catch (error) {
      this.logger.error(`CRITICAL: Failed to load required database metadata from ${DATABASE_METADATA_FILE_PATH}: ${error.message}`);
      throw new Error(`Context Service initialization failed: Cannot load database metadata from ${DATABASE_METADATA_FILE_PATH}. This file is required for the application to function.`);
    }
  }
}
