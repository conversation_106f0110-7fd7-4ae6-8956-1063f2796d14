import { <PERSON>, <PERSON>, Body, Get, Param, Delete, Logger } from '@nestjs/common';
import { SearchService, SearchRequest, SearchResponse } from './search.service';

@Controller('search')
export class SearchController {
  private readonly logger = new Logger(SearchController.name);

  constructor(private readonly searchService: SearchService) {}

  @Post()
  async search(@Body() request: SearchRequest): Promise<SearchResponse> {
    this.logger.log(`Received search request: ${JSON.stringify({ query: request.query?.substring(0, 100), conversationId: request.conversationId })}`);

    try {
      const result = await this.searchService.search(request);
      this.logger.log(`Search completed successfully for conversation: ${result.conversationId}`);
      return result;
    } catch (error) {
      this.logger.error(`Search failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Get('conversation/:id')
  async getConversation(@Param('id') conversationId: string) {
    this.logger.log(`Retrieving conversation: ${conversationId}`);
    const messages = this.searchService.getConversation(conversationId);
    this.logger.log(`Found ${messages.length} messages in conversation ${conversationId}`);

    if (messages.length === 0) {
      this.logger.log(`No conversation found for ID: ${conversationId}`);
      return {
        conversationId,
        messages: [],
        exists: false,
      };
    }

    return {
      conversationId,
      messages,
      exists: true,
    };
  }

  @Delete('conversation/:id')
  async clearConversation(@Param('id') conversationId: string) {
    this.logger.log(`Clearing conversation: ${conversationId}`);
    const cleared = this.searchService.clearConversation(conversationId);
    this.logger.log(`Conversation ${conversationId} cleared: ${cleared}`);

    return {
      conversationId,
      cleared,
    };
  }
}
