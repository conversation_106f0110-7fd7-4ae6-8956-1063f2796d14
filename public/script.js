class SearchApp {
    constructor() {
        this.conversationId = null;
        this.initializeElements();
        this.attachEventListeners();
        this.initializeFromURL();
    }

    initializeElements() {
        console.log('Initializing elements...');
        this.searchForm = document.getElementById('searchForm');
        this.queryInput = document.getElementById('queryInput');
        this.searchButton = document.getElementById('searchButton');
        this.buttonText = document.getElementById('buttonText');
        this.loadingSpinner = document.getElementById('loadingSpinner');
        this.conversationContainer = document.getElementById('conversationContainer');
        this.newConversationBtn = document.getElementById('newConversationBtn');
        this.clearConversationBtn = document.getElementById('clearConversationBtn');

        // Debug: Check if all elements are found
        const elements = {
            searchForm: this.searchForm,
            queryInput: this.queryInput,
            searchButton: this.searchButton,
            buttonText: this.buttonText,
            loadingSpinner: this.loadingSpinner,
            conversationContainer: this.conversationContainer,
            newConversationBtn: this.newConversationBtn,
            clearConversationBtn: this.clearConversationBtn
        };

        for (const [name, element] of Object.entries(elements)) {
            if (!element) {
                console.error(`Element not found: ${name}`);
            } else {
                console.log(`Element found: ${name}`);
            }
        }
    }

    attachEventListeners() {
        console.log('Attaching event listeners...');

        if (this.searchForm) {
            this.searchForm.addEventListener('submit', (e) => this.handleSearch(e));
        }

        if (this.newConversationBtn) {
            this.newConversationBtn.addEventListener('click', () => this.startNewConversation());
        }

        if (this.clearConversationBtn) {
            this.clearConversationBtn.addEventListener('click', () => this.clearCurrentConversation());
        }

        // Auto-resize textarea
        if (this.queryInput) {
            this.queryInput.addEventListener('input', () => {
                this.queryInput.style.height = 'auto';
                this.queryInput.style.height = this.queryInput.scrollHeight + 'px';
            });
        }

        console.log('Event listeners attached successfully');
    }

    async initializeFromURL() {
        // Check if there's a conversation ID in the URL
        const urlParams = new URLSearchParams(window.location.search);
        const conversationId = urlParams.get('conversation');

        if (conversationId) {
            console.log(`Loading conversation from URL: ${conversationId}`);
            await this.loadConversation(conversationId);
        } else {
            this.showEmptyState();
        }
    }

    async loadConversation(conversationId) {
        try {
            const response = await fetch(`/api/search/conversation/${conversationId}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (!data.exists || data.messages.length === 0) {
                console.log(`Conversation ${conversationId} not found or empty`);
                this.showEmptyState();
                return;
            }

            this.conversationId = conversationId;
            this.reconstructConversationUI(data.messages);

            console.log(`Loaded conversation ${conversationId} with ${data.messages.length} messages`);
        } catch (error) {
            console.error('Error loading conversation:', error);
            this.showEmptyState();
        }
    }

    reconstructConversationUI(messages) {
        // Clear existing conversation
        this.conversationContainer.innerHTML = '';

        // Skip system message and reconstruct user/assistant pairs
        for (let i = 0; i < messages.length; i++) {
            const message = messages[i];

            if (message.role === 'system') {
                continue; // Skip system messages
            }

            if (message.role === 'user') {
                this.addMessage('user', message.content);
            } else if (message.role === 'assistant' && message.content) {
                // Find any tool calls that preceded this assistant message
                const toolCalls = [];

                // Look backwards for tool calls
                for (let j = i - 1; j >= 0; j--) {
                    const prevMessage = messages[j];
                    if (prevMessage.role === 'assistant' && prevMessage.tool_calls) {
                        toolCalls.push(...prevMessage.tool_calls);
                        break;
                    }
                    if (prevMessage.role === 'user') {
                        break; // Stop at the previous user message
                    }
                }

                this.addMessage('assistant', message.content, toolCalls.length > 0 ? toolCalls : null);
            }
        }

        if (this.conversationContainer.children.length === 0) {
            this.showEmptyState();
        }
    }

    updateURL() {
        if (this.conversationId) {
            const url = new URL(window.location);
            url.searchParams.set('conversation', this.conversationId);
            window.history.replaceState({}, '', url);
        } else {
            const url = new URL(window.location);
            url.searchParams.delete('conversation');
            window.history.replaceState({}, '', url);
        }
    }

    async handleSearch(event) {
        event.preventDefault();
        
        const query = this.queryInput.value.trim();
        if (!query) return;

        this.setLoading(true);
        this.addMessage('user', query);
        this.queryInput.value = '';
        this.queryInput.style.height = 'auto';

        try {
            const response = await this.sendSearchRequest(query);
            const wasNewConversation = !this.conversationId;
            this.conversationId = response.conversationId;

            // Update URL if this is a new conversation
            if (wasNewConversation) {
                this.updateURL();
            }

            this.addMessage('assistant', response.response, response.toolCalls);
        } catch (error) {
            console.error('Search error:', error);
            this.addMessage('error', `Error: ${error.message}`);
        } finally {
            this.setLoading(false);
        }
    }

    async sendSearchRequest(query) {
        const requestBody = {
            query: query,
            conversationId: this.conversationId
        };

        const response = await fetch('/api/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    addMessage(type, content, toolCalls = null) {
        // Remove empty state if it exists
        const emptyState = this.conversationContainer.querySelector('.empty-state');
        if (emptyState) {
            emptyState.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;

        const headerDiv = document.createElement('div');
        headerDiv.className = 'message-header';
        headerDiv.textContent = type === 'user' ? 'You' : 
                               type === 'assistant' ? 'Assistant' : 'Error';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.textContent = content;

        messageDiv.appendChild(headerDiv);
        messageDiv.appendChild(contentDiv);

        // Add tool calls information if available
        // if (toolCalls && toolCalls.length > 0) {
        //     const toolCallsDiv = document.createElement('div');
        //     toolCallsDiv.className = 'tool-calls';
        //
        //     toolCalls.forEach(toolCall => {
        //         const toolDiv = document.createElement('div');
        //         toolDiv.className = 'tool-call';
        //         toolDiv.textContent = `• ${toolCall.function.name}`;
        //         toolCallsDiv.appendChild(toolDiv);
        //     });
        //
        //     messageDiv.appendChild(toolCallsDiv);
        // }

        this.conversationContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }

    setLoading(isLoading) {
        this.searchButton.disabled = isLoading;
        this.queryInput.disabled = isLoading;
        
        if (isLoading) {
            this.buttonText.classList.add('hidden');
            this.loadingSpinner.classList.remove('hidden');
        } else {
            this.buttonText.classList.remove('hidden');
            this.loadingSpinner.classList.add('hidden');
        }
    }

    scrollToBottom() {
        this.conversationContainer.scrollTop = this.conversationContainer.scrollHeight;
    }

    startNewConversation() {
        this.conversationId = null;
        this.conversationContainer.innerHTML = '';
        this.updateURL(); // Clear conversation ID from URL
        this.showEmptyState();
    }

    async clearCurrentConversation() {
        if (this.conversationId) {
            try {
                await fetch(`/api/search/conversation/${this.conversationId}`, {
                    method: 'DELETE'
                });
            } catch (error) {
                console.error('Error clearing conversation:', error);
            }
        }
        this.startNewConversation();
    }

    showEmptyState() {
        const emptyStateDiv = document.createElement('div');
        emptyStateDiv.className = 'empty-state';
        emptyStateDiv.innerHTML = `
            <h3>Welcome to SW Search Agent</h3>
            <p>Start by asking a question about your database. For example:</p>
            <ul style="text-align: left; display: inline-block; margin-top: 15px;">
                <li>"Show me all tables in the database"</li>
                <li>"What's the structure of the users table?"</li>
                <li>"Find all products with price greater than 100"</li>
                <li>"How many records are in each table?"</li>
            </ul>
        `;
        this.conversationContainer.appendChild(emptyStateDiv);
    }
}

// Initialize the app when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing SearchApp...');
    try {
        new SearchApp();
        console.log('SearchApp initialized successfully');
    } catch (error) {
        console.error('Error initializing SearchApp:', error);
    }
});
